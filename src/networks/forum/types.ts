/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/

export type VoteTypeE = 'UPVOTE' | 'DOWNVOTE';

export type ForumQuestionVoteI = {
  id: string;
  cursorId: number;
};

export type ForumQuestionVoteFetchManyI = {
  questionId: string;
  type: VoteTypeE;
  cursorId?: number | null;
  pageSize?: number;
};

export type ForumQuestionVoteCreateOneI = {
  questionId: string;
  type: VoteTypeE;
};

export type ForumQuestionVoteDeleteOneI = {
  questionId: string;
};

export type ProfileExternalI = {
  id: string;
  avatar: string | null;
  name: string;
  designationText: string | null;
  cursorId: number;
};

export type TotalCursorDataI<T> = {
  total: number;
  data: T[];
  nextCursorId: number | null;
};

export type ForumQuestionVoteFetchManyResultI = TotalCursorDataI<ProfileExternalI>;

// Answer vote types (similar structure for answers)
export type ForumAnswerVoteI = {
  id: string;
  cursorId: number;
};

export type ForumAnswerVoteFetchManyI = {
  answerId: string;
  type: VoteTypeE;
  cursorId?: number | null;
  pageSize?: number;
};

export type ForumAnswerVoteCreateOneI = {
  answerId: string;
  type: VoteTypeE;
};

export type ForumAnswerVoteDeleteOneI = {
  answerId: string;
};

export type ForumAnswerVoteFetchManyResultI = TotalCursorDataI<ProfileExternalI>;
