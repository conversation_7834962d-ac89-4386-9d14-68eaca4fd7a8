export const findValidity = (
  date: string,
  showYear: boolean,
): [number, 'Y' | 'M' | 'D'] | ['Unlimited'] | [] => {
  if (date === null || date === undefined) {
    return ['Unlimited'];
  }

  const today = new Date();
  const validityDate = new Date(date);

  if (validityDate > today) {
    const yearDiff = validityDate.getFullYear() - today.getFullYear();
    const monthDiff = validityDate.getMonth() - today.getMonth();
    const dayDiff = validityDate.getDate() - today.getDate();

    let totalMonths = yearDiff * 12 + monthDiff;

    if (dayDiff < 0) {
      totalMonths -= 1;
    }

    const diffTime = validityDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (showYear && yearDiff >= 1) {
      return [yearDiff, 'Y'];
    } else if (totalMonths >= 1) {
      return [totalMonths, 'M'];
    } else {
      return [diffDays, 'D'];
    }
  } else {
    return [];
  }
};