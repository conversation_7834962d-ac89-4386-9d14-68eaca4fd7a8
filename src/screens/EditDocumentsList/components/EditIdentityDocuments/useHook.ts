import { useCallback, useEffect, useState } from 'react';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { useDispatch } from 'react-redux';
import {
  deleteIdentityDocument,
  fetchIdentityDocuments,
} from '@/src/redux/slices/about/aboutSlice';
import { AppDispatch } from '@/src/redux/store';
import { handleError } from '@/src/utilities/errors/errors';
import { navigate } from '@/src/utilities/navigation';
import { showToast } from '@/src/utilities/toast';
import { deleteDocumentAPI, fetchIdentityDocumentsAPI } from '@/src/networks/career/document';
import { IdentityI } from '../EditDocumentationList/types';

export const useEditIdentityDocument = (profileId: string) => {
  const [page, setPage] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  const [loading, setLoading] = useState(false);
  const dispatch = useDispatch<AppDispatch>();
  const [deleteDocumentId, setDeleteDocumentId] = useState('');
  const [isVisible, setIsVisible] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [documents, setDocuments] = useState<IdentityI[]>([]);
  const navigation = useNavigation();

  const triggerErrorBoundary = (error: Error) => {
    setError(error);
  };

  if (error) {
    throw error;
  }

  useFocusEffect(
    useCallback(() => {
      const fetchInitialDocuments = async () => {
        setLoading(true);
        try {
          // await dispatch(fetchIdentityDocuments({ profileId, page: 0 })).unwrap();
          const result = await fetchIdentityDocumentsAPI(profileId, 0, 10);
          setDocuments(result);
        } catch (error) {
          triggerErrorBoundary(
            new Error(
              'Failed to load identity documents: ' +
                (error instanceof Error ? error.message : 'Unknown error'),
            ),
          );
        } finally {
          setLoading(false);
        }
      };
      fetchInitialDocuments();
    }, [profileId]),
  );

  //   const loadMoreDocuments = async () => {
  //     if (!hasMore) return;

  //     const response = await fetchIdentityDocuments(profileId, page, PAGE_SIZE);

  //     if (response.length > 0) {
  //       setDocuments(prev => [...prev, ...response]);
  //       setPage(prev => prev + 1);
  //       setHasMore(response.length === PAGE_SIZE);
  //     } else {
  //       setHasMore(false);
  //     }
  //   };

  const onEditDocument = (documentId: string) => {
    navigate('EditDocumentItem', {
      profileId,
      documentId,
      type: 'identity',
    });
  };

  const onDeleteDocument = async () => {
    setIsDeleting(true);
    try {
      await deleteDocumentAPI(deleteDocumentId, 'identity');
      // dispatch(deleteIdentityDocument(deleteDocumentId));
      setDocuments((prev) => prev.filter((item) => item.id !== deleteDocumentId));
      showToast({
        message: 'Success',
        description: 'Document deleted successfully',
        type: 'success',
      });
    } catch (error) {
      handleError(error, {
        handle4xxError: () => {
          showToast({
            message: 'Error',
            description: 'Failed to Delete Document',
            type: 'error',
          });
        },
        handle5xxError: () => {
          showToast({
            message: 'Server Error',
            description: 'Please try again later',
            type: 'error',
          });
        },
      });
    } finally {
      setIsDeleting(false);
    }
    navigation.goBack();
  };

  return {
    // loadMoreDocuments,
    onEditDocument,
    onDeleteDocument,
    hasMore,
    isVisible,
    setIsVisible,
    setDeleteDocumentId,
    isDeleting,
    loading,
    documents,
  };
};
