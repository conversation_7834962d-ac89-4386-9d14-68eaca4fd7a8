import { SetStateAction, useState } from 'react';
import React from 'react';
import { TouchableOpacity, View } from 'react-native';
import { SectionHeader } from '@/src/components/SectionHeader';
import Tabs from '@/src/components/Tabs';
import { navigate } from '@/src/utilities/navigation';
import Documentation from '@/src/assets/svgs/Document';
import EditPencil from '@/src/assets/svgs/EditPencil';
import ProfileIdentity from './components/Identity';
import ProfileVisa from './components/Visa';
import { DocumentationTabsI, ProfileDocumentationPropsI } from './types';

const ProfileDocumentation = ({ isUserProfile, profileId }: ProfileDocumentationPropsI) => {
  const tabs = [
    {
      id: 'identity',
      label: 'Identity',
    },
    {
      id: 'visa',
      label: 'Visa',
    },
  ];

  const tabScreens: DocumentationTabsI = {
    identity: <ProfileIdentity profileId={profileId} />,
    visa: <ProfileVisa profileId={profileId} />,
  };

  const [activeTab, setActiveTab] = useState<keyof DocumentationTabsI>('identity');

  return (
    <View className="pt-4">
      <View className="flex-row items-center justify-between ">
        <SectionHeader title="Documentation" icon={Documentation} />
        {isUserProfile && (
          <TouchableOpacity
            onPress={() =>
              navigate('EditDocumentList', {
                editable: true,
                tab: activeTab,
              })
            }
          >
            <EditPencil width={2.3} height={2.3} />
          </TouchableOpacity>
        )}
      </View>
      <View className="py-4">
        <Tabs
          tabs={tabs}
          activeTab={activeTab}
          onTabChange={setActiveTab as React.Dispatch<SetStateAction<string>>}
        />
        {tabScreens[activeTab]}
      </View>
    </View>
  );
};

export default ProfileDocumentation;
