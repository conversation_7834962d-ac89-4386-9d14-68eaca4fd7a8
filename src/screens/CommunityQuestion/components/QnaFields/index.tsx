import { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import ChipInput from '@/src/components/ChipInput';
import EntitySearch from '@/src/components/EntitySearch';
import { selectMultipleSelectionsByKey, selectSelectionByKey } from '@/src/redux/selectors/search';
import { setMultipleSelections } from '@/src/redux/slices/entitysearch/searchSlice';
import { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import { AppDispatch } from '@/src/redux/store';
import { showToast } from '@/src/utilities/toast';

const QnaFields = () => {
  const dispatch = useDispatch<AppDispatch>();
  const departmentSelection = useSelector(selectSelectionByKey('department'));
  const topicsSelection = useSelector(
    selectMultipleSelectionsByKey('topic'),
  ) as unknown as SearchResultI[];
  const [localTopics, setLocalTopics] = useState<SearchResultI[]>([]);

  const MAX_TOPICS = 3;

  useEffect(() => {
    if (!topicsSelection) return;

    setLocalTopics((prev) => {
      const existingIds = new Set(prev.map((t) => t.id));
      const newTopics = topicsSelection.filter((t) => !existingIds.has(t.id));
      const merged = [...prev, ...newTopics];

      if (merged.length > MAX_TOPICS) {
        showToast({
          type: 'error',
          message: 'Topic Limit Reached',
          description: `You can only select up to ${MAX_TOPICS} topics`,
        });

        const limitedTopics = merged.slice(0, MAX_TOPICS);

        dispatch(setMultipleSelections({ key: 'topic', value: limitedTopics }));
        return limitedTopics;
      }

      return merged;
    });
  }, [topicsSelection]);

  const handleTopicRemove = (id: string | number) => {
    const idString = id.toString();
    const updatedTopics = localTopics.filter((t) => t.id !== idString);
    setLocalTopics(updatedTopics);
    dispatch(setMultipleSelections({ key: 'topic', value: updatedTopics }));
  };

  return (
    <>
      <ChipInput
        title="Topics"
        placeholder="Add a topic"
        chips={localTopics}
        onRemove={handleTopicRemove}
      />

      <EntitySearch
        title={'Department Type'}
        placeholder={`Enter department type`}
        selectionKey="department"
        data={departmentSelection ? departmentSelection.name : ''}
      />
    </>
  );
};

export default QnaFields;
