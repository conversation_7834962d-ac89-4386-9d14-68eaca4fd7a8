/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { StackNavigationProp } from '@react-navigation/stack';
import { ExperienceFetchForClientResultI } from '@/src/redux/slices/experience/types';
import { ProfileStackParamsListI } from '@/src/navigation/types';

export type EditExperienceListPropsI = {
  profileId: string;
  onBack: () => void;
  editable?: boolean;
};

export interface UseEditExperienceListI {
  experiences: ExperienceFetchForClientResultI[];
  isLoading: boolean;
  isModalVisible: boolean;
  handleModal: () => void;
  onAddExperience: () => void;
  onEditExperience: (experienceId: string) => void;
  onDeleteExperience: (experienceId: string) => Promise<void>;
  navigation: StackNavigationProp<ProfileStackParamsListI>;
}

export type Duration = {
  years: number;
  months: number;
};
