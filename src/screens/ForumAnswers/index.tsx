/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { useState } from 'react';
import {
  View,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  Keyboard,
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import BackButton from '@/src/components/BackButton';
import SafeArea from '@/src/components/SafeArea';
import { LearnCollabStackParamsListI } from '@/src/navigation/types';
import AnswerInput from './AnswerInput';
import type { ForumAnswerProps } from './Answers/types';
import AnswersList from './AnswersList';

const initialAnswers: ForumAnswerProps[] = [
  {
    answerId: '1',
    postId: '1',
    userId: 'user1',
    content:
      'lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
    answerVerified: true,
    upVotes: 10,
    downVotes: 2,
    comments: 5,
    commentView: false,
    userVote: null,
  },
  {
    answerId: '2',
    postId: '1',
    userId: 'user2',
    content:
      'Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.',
    answerVerified: false,
    upVotes: 7,
    downVotes: 1,
    comments: 3,
    commentView: false,
    userVote: 'UPVOTE',
  },
  {
    answerId: '3',
    postId: '2',
    userId: 'user3',
    content:
      'Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.',
    answerVerified: true,
    upVotes: 15,
    downVotes: 0,
    comments: 8,
    commentView: false,
    userVote: null,
  },
  {
    answerId: '4',
    postId: '2',
    userId: 'user4',
    content:
      'Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.',
    answerVerified: false,
    upVotes: 4,
    downVotes: 3,
    comments: 2,
    commentView: false,
    userVote: 'DOWNVOTE',
  },
];

const ForumAnswersScreen = () => {
  const navigation = useNavigation<StackNavigationProp<LearnCollabStackParamsListI>>();
  const route = useRoute<RouteProp<LearnCollabStackParamsListI, 'ForumAnswers'>>();
  const { postId } = route.params;

  const [answers, setAnswers] = useState<ForumAnswerProps[]>(initialAnswers);

  const handleSubmit = (text: string) => {
    setAnswers((prev) => [
      ...prev,
      {
        answerId: '5',
        postId: postId, // Use the actual postId from route params
        userId: 'currentUser',
        content: text,
        answerVerified: false,
        upVotes: 0,
        downVotes: 0,
        comments: 0,
        commentView: false,
        userVote: null,
      },
    ]);
  };

  const handleAnswerVoteUpdate = (
    answerId: string,
    newVote: 'UPVOTE' | 'DOWNVOTE' | null,
    upVoteDelta: number,
    downVoteDelta: number
  ) => {
    setAnswers((prev) =>
      prev.map((answer) =>
        answer.answerId === answerId
          ? {
              ...answer,
              userVote: newVote,
              upVotes: Math.max(0, answer.upVotes + upVoteDelta),
              downVotes: Math.max(0, answer.downVotes + downVoteDelta),
            }
          : answer
      )
    );
  };
  return (
    <SafeArea>
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <KeyboardAvoidingView
          className="flex-1"
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          keyboardVerticalOffset={Platform.OS === 'ios' ? 10 : 20}
        >
          <View className="flex-row items-center px-4">
            <BackButton onBack={() => navigation.goBack()} label="" />
          </View>
          <AnswersList answers={answers} onVoteUpdate={handleAnswerVoteUpdate} />
          <AnswerInput onSubmit={handleSubmit} />
        </KeyboardAvoidingView>
      </TouchableWithoutFeedback>
    </SafeArea>
  );
};

export default ForumAnswersScreen;
