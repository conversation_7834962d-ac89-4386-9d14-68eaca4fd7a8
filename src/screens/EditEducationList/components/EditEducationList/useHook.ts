import { useCallback, useEffect, useState } from 'react';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useDispatch } from 'react-redux';
import { deleteEducation, fetchEducations } from '@/src/redux/slices/about/aboutSlice';
import { AppDispatch } from '@/src/redux/store';
import { handleError } from '@/src/utilities/errors/errors';
import { showToast } from '@/src/utilities/toast';
import { ProfileStackParamsListI } from '@/src/navigation/types';
import { deleteEducationAPI, fetchEducationsAPI } from '@/src/networks/career/education';
import { EducationI, UseEditEducationListI } from './types';

export const useEditEducationList = (profileId: string): UseEditEducationListI => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const navigation = useNavigation<StackNavigationProp<ProfileStackParamsListI>>();
  const [loading, setLoading] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const [deleteEducationId, setDeleteEducationId] = useState<string>('');
  const [isDeleting, setIsDeleting] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const dispatch = useDispatch<AppDispatch>();
  const [educations, setEducations] = useState<EducationI[]>([]);

  const triggerErrorBoundary = (error: Error) => {
    setError(error);
  };

  if (error) {
    throw error;
  }

  useFocusEffect(
    useCallback(() => {
      const fetchInitialEducations = async () => {
        setLoading(true);
        try {
          const result = await fetchEducationsAPI(profileId, 0, 10);
          // await dispatch(fetchEducations({ profileId, page: 0 })).unwrap();
          setEducations(result);
        } catch (error) {
          triggerErrorBoundary(
            new Error(
              'Failed to load educations: ' +
                (error instanceof Error ? error.message : 'Unknown error'),
            ),
          );
        } finally {
          setLoading(false);
        }
      };
      fetchInitialEducations();
    }, [profileId]),
  );

  const onAddEducation = () => {
    navigation.navigate('EditEducationItem', { profileId });
  };

  const onEditEducation = async (educationId: string) => {
    // await dispatch(setEducationAsync({ educationId }));
    navigation.navigate('EditEducationItem', {
      profileId,
      educationId,
    });
  };

  const onDeleteEducation = async () => {
    try {
      setIsDeleting(true);
      await deleteEducationAPI(deleteEducationId);
      // dispatch(deleteEducation(deleteEducationId));
      setEducations((prev) => prev.filter((item) => item.id !== deleteEducationId));
      showToast({
        message: 'Success',
        description: 'Education deleted successfully',
        type: 'success',
      });
      navigation.goBack();
    } catch (error) {
      handleError(error, {
        handle4xxError: () => {
          showToast({
            message: 'Failed to Delete Education',
            type: 'error',
          });
        },
        handle5xxError: () => {
          showToast({
            message: 'Server Error',
            description: 'Please try again later',
            type: 'error',
          });
        },
      });
    } finally {
      setIsDeleting(false);
    }
  };

  return {
    isSubmitting,
    onAddEducation,
    onEditEducation,
    onDeleteEducation,
    navigation,
    loading,
    isVisible,
    setIsVisible,
    setDeleteEducationId,
    isDeleting,
    educations,
  };
};
