import { ActivityIndicator, FlatList, Pressable, Text, View } from 'react-native';
import { useSelector } from 'react-redux';
import BackButton from '@/src/components/BackButton';
import CustomModal from '@/src/components/Modal';
import NotFound from '@/src/components/NotFound';
import TextView from '@/src/components/TextView';
import { selectEducations } from '@/src/redux/selectors/about';
import AddItem from '@/src/assets/svgs/AddItem';
import EditPencil from '@/src/assets/svgs/EditPencil';
import DeleteIcon from '@/src/assets/svgs/TrashBin';
import { formatDate } from '../utils';
import { EducationI } from './types';
import { useEditEducationList } from './useHook';
import { useNavigation } from '@react-navigation/native';

const EditEducationList = ({
  onBack,
  profileId,
  editable,
}: {
  onBack: () => void;
  profileId: string;
  editable: boolean;
}) => {
  // const educations = useSelector(selectEducations);
  const navigation = useNavigation();

  const {
    isSubmitting,
    onAddEducation,
    onEditEducation,
    onDeleteEducation,
    loading,
    isVisible,
    setIsVisible,
    setDeleteEducationId,
    isDeleting,
    educations,
  } = useEditEducationList(profileId);

  if (loading) {
    return (
      <View className="flex-1 justify-center items-center">
        <ActivityIndicator size="large" color="#448600" />
      </View>
    );
  }

  const renderEducationItem = ({ item, index }: { item: EducationI; index: number }) => (
    <View className={`${index === educations.length - 1 ? `` : `border-b border-[#D4D4D4]`} p-4`}>
      <TextView
        subtitle={item.entity.name}
        subtitleClassName="font-medium leading-22 mt-0 font-medium text-[#000000]"
      />
      <TextView
        subtitle={item.degree.name}
        subtitleClassName="text-sm font-normal leading-[18px] mt-1 text-[#000000]"
      />
      <TextView
        subtitle={`${formatDate(item.fromDate)} - ${item.toDate ? formatDate(item.toDate) : 'Present'}`}
        subtitleClassName="text-sm font-normal-colors-neutral-500 mt-1 text-[#737373]"
      />
      {editable && (
        <View className="flex-row justify-end">
          <Pressable onPress={() => onEditEducation(item.id)} className="p-2">
            <EditPencil width={2.6} height={2.6} />
          </Pressable>
          <Pressable
            onPress={() => {
              setDeleteEducationId(item.id);
              setIsVisible(true);
            }}
            className="p-2"
          >
            <DeleteIcon width={2.6} height={2.6} />
          </Pressable>
        </View>
      )}
    </View>
  );

  return (
    <View className="flex-1 px-4">
      <View className="flex-row items-center justify-between">
        <View className="flex-row items-center gap-1">
          <BackButton onBack={navigation.goBack} label="" />
          <Text className="text-xl font-medium">Edit Educations</Text>
        </View>
        {editable && 
          <Pressable onPress={onAddEducation}>
            <AddItem />
          </Pressable>
        }
      </View>
      {educations.length === 0 ? (
        <NotFound />
      ) : (
        <>
          <FlatList
            data={educations}
            contentContainerStyle={{
              flexGrow: 1,
              backgroundColor: 'white',
              paddingVertical: 20,
            }}
            renderItem={({ item, index }) => renderEducationItem({ item, index })}
            keyExtractor={(item) => item.id}
            className="mt-5"
            showsHorizontalScrollIndicator={false}
          />
          <CustomModal
            isVisible={isVisible}
            onCancel={() => setIsVisible(false)}
            title="Are you sure you want to delete this education?"
            confirmText="Delete"
            confirmButtonVariant="danger"
            onConfirm={onDeleteEducation}
            isConfirming={isDeleting}
          />
        </>
      )}
    </View>
  );
};

export default EditEducationList;
