import { Dispatch, SetStateAction } from 'react';
import { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import { ExperienceFormDataI } from './schema';

export interface EditExperienceItemPropsI {
  onBack: () => void;
  profileId: string;
  experienceId?: string;
}

export type DesignationsI = {
  indexKey: string;
  id: string;
  name: string;
  dataType: 'raw' | 'master';
};

export type apiResponseTypeI = {
  entity: SearchResultI;
  designations: any[];
};

export type FetchedExperienceI = {
  company: SearchResultI;
  designations: any[];
};

export type FieldTypeI = {
  designation: any;
  fromDate: string;
  id: string;
  ships: any[];
  toDate?: string;
};

export type FieldShipType = {
  fromDate: string;
  id: string;
  name: string;
  ship: any;
  subVesselType: any;
  toDate?: string;
  isPresent?: boolean;
};

export type DesignationWithDateI = {
  designation?: any;
  fromDate?: string;
  toDate?: string;
  id?: string;
  ships?: any[];
  isPresent?: boolean;
  name?: string;
  experienceDesignationId?: string;
};

export type ShipItemProps = {
  field: FieldTypeI;
  onAddEdit: (field: FieldTypeI, shipId?: string) => void;
  deleteShip: (field: FieldTypeI, shipId: string) => void;
  isDeleting: boolean;
  company: SearchResultI;
};

export type UseEditExperienceItemReturn = {
  designations: DesignationsI[];
  isSubmitting: boolean;
  loading: boolean;
  handleSubmit: () => Promise<void>;
  handleAddDesignation: () => void;
  handleAddEditShip: (field: FieldTypeI, shipId?: string) => void;
  handleDeleteShip: (field: FieldTypeI, shipId: string) => Promise<void>;
  isDeleting: boolean;
  localDesignations: DesignationWithDateI[];
  getDateChangeHandler: (
    index: number,
    field: 'fromDate' | 'toDate',
  ) => React.Dispatch<React.SetStateAction<Date>>;
  localEntity: SearchResultI | undefined;
  setLocalDesignations: Dispatch<SetStateAction<DesignationWithDateI[]>>;
  getFieldError: (fieldName: string, index: number) => string | undefined;
  handlePresentCheckbox: (index: number, isPresent: boolean) => void;
};

export type { ExperienceFormDataI };
