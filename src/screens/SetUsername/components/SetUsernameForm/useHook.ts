import { useEffect, useState, useRef } from 'react';
import { useNavigation } from '@react-navigation/native';
import type { StackNavigationProp } from '@react-navigation/stack';
import { useForm } from 'react-hook-form';
import { useDispatch } from 'react-redux';
import { saveUsernameAsync } from '@/src/redux/slices/user/userSlice';
import type { AppDispatch } from '@/src/redux/store';
import { handleError } from '@/src/utilities/errors/errors';
import { showToast } from '@/src/utilities/toast';
import APIResError from '@/src/errors/networks/APIResError';
import type { AppStackParamListI } from '@/src/navigation/types';
import { checkUsernameAPI } from '@/src/networks/profile/username';
import type { SetUsernameFormDataI } from './types';

const useSetUsername = (email: string) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [validationStatus, setValidationStatus] = useState<
    'idle' | 'checking' | 'valid' | 'invalid'
  >('idle');
  const [errorMessage, setErrorMessage] = useState<string | undefined>();

  const navigation = useNavigation<StackNavigationProp<AppStackParamListI>>();
  const dispatch = useDispatch<AppDispatch>();
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastCheckedUsernameRef = useRef<string>('');

  const methods = useForm<SetUsernameFormDataI>({
    mode: 'onChange',
    defaultValues: {
      userName: '',
      email,
    },
  });

  const { watch, setError, clearErrors } = methods;
  const userName = watch('userName').trim();

  useEffect(() => {
    if (email) {
      methods.setValue('email', email);
    }
  }, [email, methods]);

  const checkUsername = async (username: string) => {
    if (!username || username.length < 4) {
      setValidationStatus('idle');
      setErrorMessage(undefined);
      lastCheckedUsernameRef.current = '';
      clearErrors('userName');
      return;
    }

    if (username === lastCheckedUsernameRef.current) {
      return;
    }

    setValidationStatus('checking');
    lastCheckedUsernameRef.current = username;

    try {
      await checkUsernameAPI({ username });
      setValidationStatus('valid');
      setErrorMessage(undefined);
      clearErrors('userName');
    } catch (error) {
      setValidationStatus('invalid');
      if (error instanceof APIResError && error.status === 409) {
        setErrorMessage('Username is already taken');
      } else {
        setErrorMessage('Error checking username');
        showToast({
          message: 'Server Error',
          description: 'Please try again later',
          type: 'error',
        });
      }
    }
  };

  useEffect(() => {
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    const formError = methods.formState.errors.userName?.message;
    if (formError) {
      setErrorMessage(formError);
      setValidationStatus('invalid');
      return;
    }

    if (userName.length === 0) {
      setValidationStatus('idle');
      setErrorMessage(undefined);
      lastCheckedUsernameRef.current = '';
      return;
    }

    if (userName.length < 4) {
      setValidationStatus('idle');
      setErrorMessage(undefined);
      lastCheckedUsernameRef.current = '';
      return;
    }

    setErrorMessage(undefined);
    debounceTimeoutRef.current = setTimeout(() => {
      checkUsername(userName);
    }, 800);

    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, [userName, methods.formState.errors.userName]);

  const onSubmit = async (data: SetUsernameFormDataI) => {
    if (data.userName.trim().length < 4) {
      setError('userName', {
        message: 'Username must be at least 4 characters',
      });
      return;
    }

    setIsSubmitting(true);
    try {
      await dispatch(
        saveUsernameAsync({
          username: data.userName.trim(),
        }),
      ).unwrap();
      navigation.navigate('AddUserDetailScreen');
    } catch (error) {
      setIsSubmitting(false);
      handleError(error);
    }
  };

  const canSubmit = methods.formState.isValid && validationStatus === 'valid';
  const isCheckingUsername = validationStatus === 'checking';

  return {
    methods,
    isSubmitting,
    onSubmit,
    canSubmit,
    errorMessage,
    isCheckingUsername,
  };
};

export default useSetUsername;
