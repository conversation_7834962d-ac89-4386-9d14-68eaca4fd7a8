/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { View, Text, Pressable } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { ScrollView } from 'react-native-gesture-handler';
import BackButton from '@/src/components/BackButton';
import SafeArea from '@/src/components/SafeArea';
import Filter from './Filter';
import useForumFilter from './Filter/useHook';

const ForumFilterScreen = () => {
  const navigation = useNavigation();
  const { applyFilters, clearAllFilters } = useForumFilter();

  return (
    <SafeArea>
      <View className="flex-row items-center justify-between px-4">
        <View className="flex-row items-center">
          <BackButton onBack={() => navigation.goBack()} label="" />
          <Text className="text-xl font-medium text-black">Filters</Text>
        </View>
        <View className="flex-row items-center gap-2">
          <Pressable className="px-2 items-center" onPress={clearAllFilters}>
            <Text className="text-base font-medium text-gray-600">Clear</Text>
          </Pressable>
          <Pressable className="px-4 items-center" onPress={applyFilters}>
            <Text className="text-xl font-medium text-[#448600]">Apply</Text>
          </Pressable>
        </View>
      </View>
      <ScrollView showsVerticalScrollIndicator={false}>
        <Filter />
      </ScrollView>
    </SafeArea>
  );
};

export default ForumFilterScreen;
