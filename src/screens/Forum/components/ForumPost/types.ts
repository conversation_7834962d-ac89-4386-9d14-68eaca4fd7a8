/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import type { ForumQuestionI, TopicI } from '@/src/networks/question/types';

type Topic = { id: string; label: string };
type Equipment = { id: string; label: string };

export type PreviewIconType = 'photo' | 'pdf' | 'video' | 'excel' | 'link';

export type AttachmentI = {
  fileUrl: string;
  fileExtension: string;
};

export type ForumPostProps = {
  postId: string;
  type: 'troubleshooting' | 'question';
  topics?: Topic[];
  equipment?: Equipment[];
  heading: string;
  solved: boolean;
  description?: string;
  previewIcons: PreviewIconType[];
  upVotes: number;
  downVotes: number;
  answers: number;
  comments?: number;
  community?: string;
  endTime: number;
  answerView: boolean;
  attachments?: AttachmentI[];
  canModify?: boolean
};

export type ForumQuestionPostProps = {
  question: ForumQuestionI;
  attachments?: AttachmentI[];
};

export type FileViewerModalProps = {
  isVisible: boolean;
  onClose: () => void;
  attachments: AttachmentI[];
  initialIndex?: number;
};
