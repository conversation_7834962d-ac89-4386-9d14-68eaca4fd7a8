/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/

import { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import type { AppDispatch } from '@/src/redux/store';
import {
  addQuestionVoteOptimistic,
  removeQuestionVoteOptimistic,
  switchQuestionVoteOptimistic,
} from '@/src/redux/slices/question/questionSlice';
import { selectForumQuestions } from '@/src/redux/selectors/question';
import {
  createQuestionVoteAPI,
  deleteQuestionVoteAPI,
  createAnswerVoteAPI,
  deleteAnswerVoteAPI,
} from '@/src/networks/forum/vote';
import { showToast } from '@/src/utilities/toast';
import type { VoteTypeE } from '@/src/networks/forum/types';

export const useQuestionVoting = (questionId: string) => {
  const dispatch = useDispatch<AppDispatch>();
  const forumQuestions = useSelector(selectForumQuestions);
  const [isLoading, setIsLoading] = useState(false);

  const question = forumQuestions.find(q => q.id === questionId);
  const currentVote = question?.vote || null;


  const handleVote = async (type: VoteTypeE) => {
    if (isLoading) return;

    setIsLoading(true);

    try {
      if (currentVote === type) {
        dispatch(removeQuestionVoteOptimistic({ questionId, type }));

        await deleteQuestionVoteAPI({ questionId });
      }
      else if (currentVote && currentVote !== type) {
        dispatch(switchQuestionVoteOptimistic({ questionId, fromType: currentVote, toType: type }));

        await deleteQuestionVoteAPI({ questionId });
        await createQuestionVoteAPI({ questionId, type });
      }
      else {
        dispatch(addQuestionVoteOptimistic({ questionId, type }));
        await createQuestionVoteAPI({ questionId, type });
      }
    } catch (error) {
      if (currentVote === type) {
        dispatch(addQuestionVoteOptimistic({ questionId, type }));
      } else if (currentVote && currentVote !== type) {
        dispatch(switchQuestionVoteOptimistic({ questionId, fromType: type, toType: currentVote }));
      } else {
        dispatch(removeQuestionVoteOptimistic({ questionId, type }));
      }

      showToast({
        type: 'error',
        message: 'Vote Failed',
        description: 'Failed to register your vote. Please try again.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleUpvote = () => handleVote('UPVOTE');
  const handleDownvote = () => handleVote('DOWNVOTE');

  return {
    currentVote,
    isLoading,
    handleUpvote,
    handleDownvote,
    isUpvoted: currentVote === 'UPVOTE',
    isDownvoted: currentVote === 'DOWNVOTE',
  };
};

export const useAnswerVoting = (
  answerId: string,
  currentVote: VoteTypeE | null = null,
  onVoteUpdate?: (answerId: string, newVote: VoteTypeE | null, upVoteDelta: number, downVoteDelta: number) => void
) => {
  const [isLoading, setIsLoading] = useState(false);

  const handleVote = async (type: VoteTypeE) => {
    if (isLoading) return;

    setIsLoading(true);
    const previousVote = currentVote;
    let upVoteDelta = 0;
    let downVoteDelta = 0;
    let newVote: VoteTypeE | null = null;

    try {
      if (currentVote === type) {
        newVote = null;
        if (type === 'UPVOTE') {
          upVoteDelta = -1;
        } else {
          downVoteDelta = -1;
        }

        onVoteUpdate?.(answerId, newVote, upVoteDelta, downVoteDelta);
        await deleteAnswerVoteAPI({ answerId });
      }
      else if (currentVote && currentVote !== type) {
        newVote = type;
        if (currentVote === 'UPVOTE') {
          upVoteDelta = -1;
          downVoteDelta = 1;
        } else {
          upVoteDelta = 1;
          downVoteDelta = -1;
        }

        onVoteUpdate?.(answerId, newVote, upVoteDelta, downVoteDelta);

        await deleteAnswerVoteAPI({ answerId });
        await createAnswerVoteAPI({ answerId, type });
      }
      else {
        newVote = type;
        if (type === 'UPVOTE') {
          upVoteDelta = 1;
        } else {
          downVoteDelta = 1;
        }

        onVoteUpdate?.(answerId, newVote, upVoteDelta, downVoteDelta);
        await createAnswerVoteAPI({ answerId, type });
      }
    } catch (error) {
      const revertUpVoteDelta = -upVoteDelta;
      const revertDownVoteDelta = -downVoteDelta;
      onVoteUpdate?.(answerId, previousVote, revertUpVoteDelta, revertDownVoteDelta);

      showToast({
        type: 'error',
        message: 'Vote Failed',
        description: 'Failed to register your vote. Please try again.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleUpvote = () => handleVote('UPVOTE');
  const handleDownvote = () => handleVote('DOWNVOTE');

  return {
    isLoading,
    handleUpvote,
    handleDownvote,
  };
};
