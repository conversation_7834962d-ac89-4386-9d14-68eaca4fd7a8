/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/

import { ForumQuestionI, ForumQuestionResultI, QuestionI } from '@/src/networks/question/types';
import type { AttachmentI, PreviewIconType } from './types';

export const getPreviewIconsFromAttachments = (attachments: AttachmentI[]): PreviewIconType[] => {
  const iconTypes = new Set<PreviewIconType>();

  attachments?.forEach((attachment) => {
    const { fileExtension } = attachment;

    if (fileExtension.startsWith('image/')) {
      iconTypes.add('photo');
    } else if (fileExtension === 'pdf') {
      iconTypes.add('pdf');
    } else if (fileExtension.startsWith('video/')) {
      iconTypes.add('video');
    } else if (
      fileExtension.includes('excel') ||
      fileExtension.includes('spreadsheet') ||
      fileExtension === 'application/vnd.ms-excel' ||
      fileExtension === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ) {
      iconTypes.add('excel');
    }
  });

  return Array.from(iconTypes);
};

export const transformQuestionToForumPost = (
  question: ForumQuestionResultI,
  attachments: AttachmentI[] = [],
) => {
  const previewIcons = getPreviewIconsFromAttachments(attachments);
  
  // Transform topics
  const topics = question.topics?.map((topic) => ({
    id: topic.id || '',
    label: topic.name || '',
  })) || [];

  // Transform equipment for troubleshooting questions
  const equipment = [];
  if (question.type === 'TROUBLESHOOT') {
    if (question.equipmentCategory) {
      equipment.push({
        id: question.equipmentCategory.id,
        label: question.equipmentCategory.name,
      });
    }
    if (question.equipmentManufacturer) {
      equipment.push({
        id: question.equipmentManufacturer.id,
        label: question.equipmentManufacturer.name,
      });
    }
    if (question.equipmentModel) {
      equipment.push({
        id: question.equipmentModel.id,
        label: question.equipmentModel.name,
      });
    }
  }

  return {
    postId: question.id,
    canModify: question.canModify,
    type: question.type === 'TROUBLESHOOT' ? ('troubleshooting' as const) : ('question' as const),
    topics: topics.length > 0 ? topics : undefined,
    equipment: equipment.length > 0 ? equipment : undefined,
    heading: question.title,
    solved: question.isSolved,
    description: question.description,
    previewIcons,
    upVotes: question.upvoteCount,
    downVotes: question.downvoteCount,
    answers: question.answerCount,
    endTime: new Date(question.liveStartedAt).getTime() + (24 * 60 * 60 * 1000),
    answerView: false,
    attachments,
  };
};
