/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/

import { useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useFocusEffect } from '@react-navigation/native';
import type { AppDispatch } from '@/src/redux/store';
import { showToast } from '@/src/utilities/toast';
import APIResError from '@/src/errors/networks/APIResError';
import {
  fetchForumQuestions,
  setForumQuestionsFilters,
  clearForumQuestions,
} from '@/src/redux/slices/question/questionSlice';
import {
  selectForumQuestions,
  selectForumQuestionsLoading,
  selectForumQuestionsRefreshing,
  selectForumQuestionsPagination,
  selectForumQuestionsFilters,
} from '@/src/redux/selectors/question';
import type { UseForumPostListResult } from './types';

const useForumPostList = (): UseForumPostListResult => {
  const dispatch = useDispatch<AppDispatch>();

  const questions = useSelector(selectForumQuestions);
  const loading = useSelector(selectForumQuestionsLoading);
  const refreshing = useSelector(selectForumQuestionsRefreshing);
  const pagination = useSelector(selectForumQuestionsPagination);
  const filters = useSelector(selectForumQuestionsFilters);
  const fetchLatestQuestions = async () => {
    try {
      await dispatch(
        fetchForumQuestions({
          refresh: true,
        }),
      ).unwrap();
    } catch (error) {
      if (!questions.length) {
        const errorMessage = `Failed to fetch questions: ${
          error instanceof APIResError ? error.message : 'Unknown error'
        }`;
        throw new Error(errorMessage);
      } else {
        showToast({ message: 'Failed to refresh questions', type: 'error' });
      }
    }
  };

  useFocusEffect(
    useCallback(() => {
      fetchLatestQuestions();
    }, [filters]),
  );

  const handleRefresh = async () => {
    try {
      await dispatch(
        fetchForumQuestions({
          refresh: true,
        }),
      ).unwrap();
    } catch (error) {
      showToast({ message: 'Failed to refresh questions', type: 'error' });
    }
  };

  const handleLoadMore = async () => {
    if (loading || !pagination.hasMore) {
      return;
    }

    try {
      await dispatch(
        fetchForumQuestions({
          refresh: false,
        }),
      ).unwrap();
    } catch (error) {
      showToast({ message: 'Failed to load more questions', type: 'error' });
    }
  };

  const toggleLiveMode = () => {
    const newIsLive = !filters.isLive;
    dispatch(setForumQuestionsFilters({ isLive: newIsLive }));
    dispatch(clearForumQuestions());
  };

  return {
    questions,
    loading,
    refreshing,
    hasMore: pagination.hasMore,
    isLive: filters.isLive,
    handleRefresh,
    handleLoadMore,
    toggleLiveMode,
  };
};

export default useForumPostList;
