/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/

import React from 'react';
import { View, Text, TouchableOpacity, FlatList, Linking } from 'react-native';
import Modal from 'react-native-modal';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Close from '@/src/assets/svgs/Close';
import ChevronDown from '@/src/assets/svgs/ChevronDown';
import PdfPreview from '@/src/assets/svgs/PdfPreview';
import ExcelPreview from '@/src/assets/svgs/ExcelPreview';
import VideoPreview from '@/src/assets/svgs/VideoPreview';
import PhotoPreview from '@/src/assets/svgs/PhotoPreview';
import ImageViewer from '@/src/components/ImageViewer';
import { showToast } from '@/src/utilities/toast';
import type { FileViewerModalProps, AttachmentI } from '../ForumPost/types';
import Download from '@/src/assets/svgs/Download';

const getFileIcon = (mimeType: string) => {
  if (mimeType.startsWith('image/')) return PhotoPreview;
  if (mimeType === 'pdf') return PdfPreview;
  if (mimeType.startsWith('video/')) return VideoPreview;
  if (mimeType.includes('excel') || mimeType.includes('spreadsheet')) return ExcelPreview;
  return PdfPreview; // Default icon
};

const getFileTypeLabel = (mimeType: string) => {
  if (mimeType.startsWith('image/')) return 'Image';
  if (mimeType === 'application/pdf') return 'PDF';
  if (mimeType.startsWith('video/')) return 'Video';
  if (mimeType.includes('excel') || mimeType.includes('spreadsheet')) return 'Excel';
  return 'File';
};

const FileViewer = ({ isVisible, onClose, attachments }: FileViewerModalProps) => {
  const insets = useSafeAreaInsets();
  const [imageViewerVisible, setImageViewerVisible] = React.useState(false);
  const [selectedImageIndex, setSelectedImageIndex] = React.useState(0);

  const imageAttachments = attachments.filter((attachment) =>
    attachment.fileExtension.startsWith('image/'),
  );

  const handleDownload = async (fileUrl: string) => {
    try {
      const supported = await Linking.canOpenURL(fileUrl);

      if (supported) {
        await Linking.openURL(fileUrl);
        showToast({
          message: 'Success',
          description: 'Download Started',
          type: 'success',
        });
      } else {
        showToast({
          message: 'Error',
          description: 'Download Not Supported',
          type: 'error',
        });
      }
    } catch (error) {
      showToast({
        message: 'Error',
        description: 'Download Failed',
        type: 'error',
      });
    }
  };

  const handleImagePress = (attachment: AttachmentI) => {
    const imageIndex = imageAttachments.findIndex((img) => img.fileUrl === attachment.fileUrl);
    setSelectedImageIndex(imageIndex >= 0 ? imageIndex : 0);
    setImageViewerVisible(true);
  };

  const renderAttachmentItem = ({ item }: { item: AttachmentI }) => {
    const IconComponent = getFileIcon(item.fileExtension);
    const fileTypeLabel = getFileTypeLabel(item.fileExtension);
    const isImage = item.fileExtension.startsWith('image/');
    const fileName = item.fileExtension + `File - ${Date.now()}` || 'file'

    return (
      <TouchableOpacity
        className="flex-row items-center justify-between p-4 border-b border-gray-200"
        onPress={() => (isImage ? handleImagePress(item) : handleDownload(item.fileUrl))}
      >
        <View className="flex-row items-center flex-1">
          <View className="mr-3">
            <IconComponent width={2} height={2} />
          </View>
          <View className="flex-1">
            <Text className="text-base font-medium text-gray-900" numberOfLines={1}>
              {fileName}
            </Text>
            <Text className="text-sm text-gray-500">{fileTypeLabel}</Text>
          </View>
        </View>
        {!isImage && (
          <TouchableOpacity
            className="ml-3 p-2"
            onPress={() => handleDownload(item.fileUrl)}
          >
            <Download width={2} height={2} />
          </TouchableOpacity>
        )}
      </TouchableOpacity>
    );
  };


  const imageViewerPost = {
    id: 'forum-attachments',
    createdAt: new Date().toISOString(),
    Profile: {
      name: 'Forum User',
      avatar: '',
      designation: { name: '' },
      entity: { name: '' },
    },
    Media: imageAttachments.map((attachment, index) => ({
      fileUrl: attachment.fileUrl,
      extension: attachment.fileExtension,
      caption: attachment.fileExtension + `File - ${index + 1}` || 'file',
    })),
  };

  return (
    <View>
      <Modal
        isVisible={isVisible}
        onBackdropPress={onClose}
        onBackButtonPress={onClose}
        style={{ margin: 0 }}
        animationIn="slideInUp"
        animationOut="slideOutDown"
      >
        <View className="flex-1 bg-white" style={{ paddingTop: insets.top }}>
          <View className="flex-row items-center justify-between p-4 border-b border-gray-200">
            <Text className="text-lg font-semibold text-gray-900">Attachments</Text>
            <TouchableOpacity onPress={onClose} className="p-2">
              <Close stroke="#374151" width={2} height={2} />
            </TouchableOpacity>
          </View>

          <FlatList
            data={attachments}
            keyExtractor={(item) => item.fileUrl}
            renderItem={renderAttachmentItem}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{ flexGrow: 1 }}
          />
        </View>
      </Modal>

      {imageViewerVisible && imageAttachments.length > 0 && (
        <ImageViewer
          isVisible={imageViewerVisible}
          onClose={() => setImageViewerVisible(false)}
          post={imageViewerPost as any}
          initialIndex={selectedImageIndex}
        />
      )}
    </View>
  );
};

export default FileViewer;
