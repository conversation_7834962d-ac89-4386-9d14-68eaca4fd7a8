import { useCallback, useEffect, useState } from 'react';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useDispatch } from 'react-redux';
import {
  deleteValueAddedCertification,
  fetchValueAddedCertifications,
} from '@/src/redux/slices/about/aboutSlice';
import { AppDispatch } from '@/src/redux/store';
import { handleError } from '@/src/utilities/errors/errors';
import { navigate } from '@/src/utilities/navigation';
import { showToast } from '@/src/utilities/toast';
import { ProfileStackParamsListI } from '@/src/navigation/types';
import {
  deleteCertificationAPI,
  fetchValueAddedCertificationsAPI,
} from '@/src/networks/career/certification';
import { CertificationI } from '../EditCertificationList/types';

export const useEditValueAdded = (profileId: string) => {
  const [loading, setLoading] = useState(false);
  const dispatch = useDispatch<AppDispatch>();
  const [isVisible, setIsVisible] = useState(false);
  const [deleteCertificationId, setDeleteCertificationId] = useState<string>('');
  const [isDeleting, setIsDeleting] = useState(false);
  const navigation = useNavigation<StackNavigationProp<ProfileStackParamsListI>>();
  const [certifications, setCertifications] = useState<CertificationI[]>([]);

  const [error, setError] = useState<Error | null>(null);

  const triggerErrorBoundary = (error: Error) => {
    setError(error);
  };

  if (error) throw error;

  useFocusEffect(
    useCallback(() => {
      const fetchInitialCertifications = async () => {
        setLoading(true);
        try {
          // dispatch(fetchStatutoryCertifications({ profileId, page: 0 }));
          const result = await fetchValueAddedCertificationsAPI(profileId, 0, 10);
          setCertifications(result);
        } catch (error) {
          triggerErrorBoundary(
            new Error(
              'Failed to load statutory certifications: ' +
                (error instanceof Error ? error.message : 'Unknown error'),
            ),
          );
        } finally {
          setLoading(false);
        }
      };
      fetchInitialCertifications();
    }, [profileId]),
  );

  const onEditCertification = (certificationId: string) => {
    navigate('EditCertificationItem', {
      profileId,
      certificationId,
    });
  };

  const onDeleteCertification = async () => {
    setIsDeleting(true);
    try {
      await deleteCertificationAPI(deleteCertificationId);
      // dispatch(deleteValueAddedCertification(deleteCertificationId));
      setCertifications((prev) => prev.filter((item) => item.id !== deleteCertificationId));
      showToast({
        message: 'Success',
        description: 'Cerification deleted successfully',
        type: 'success',
      });
      navigation.goBack();
    } catch (error) {
      handleError(error, {
        handle4xxError: () => {
          showToast({
            message: 'Error',
            description: 'Failed to delete certification',
            type: 'error',
          });
        },
        handle5xxError: () => {
          showToast({
            message: 'Server Error',
            description: 'Please try again later',
            type: 'error',
          });
        },
      });
    } finally {
      setIsDeleting(false);
    }
  };

  return {
    onEditCertification,
    onDeleteCertification,
    isVisible,
    setIsVisible,
    setDeleteCertificationId,
    isDeleting,
    loading,
    certifications,
  };
};
