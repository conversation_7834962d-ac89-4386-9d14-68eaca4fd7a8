name: 'Upload to Google Play Console'
description: 'Uploads Android AAB bundle to Google Play Console'

inputs:
  package-name:
    description: 'Android package name'
    required: true
  track:
    description: 'Release track (internal, alpha, production) - will be auto-determined from branch if not provided'
    required: false
  service-account-json:
    description: 'Google Play service account JSON'
    required: true
  aab-path:
    description: 'Path to AAB file'
    required: true
    default: 'android/app/build/outputs/bundle/release/app-release.aab'

runs:
  using: 'composite'
  steps:
    - name: Determine Release Track
      shell: bash
      run: |
        echo "🔍 Determining release track"
        
        BRANCH_NAME="${GITHUB_REF#refs/heads/}"
        echo "Current branch: $BRANCH_NAME"
        
        if [ -n "${{ inputs.track }}" ]; then
          TRACK="${{ inputs.track }}"
          echo "Using provided track: $TRACK"
        else
          case "$BRANCH_NAME" in
            "android-prod-release")
              TRACK="production"
              echo "Branch matches android-prod-release, using production track"
              ;;
            "android-alpha-release")
              TRACK="alpha"
              echo "Branch matches android-alpha-release, using alpha track"
              ;;
            *)
              TRACK="internal"
              echo "Branch doesn't match known patterns, defaulting to internal track"
              ;;
          esac
        fi
        
        echo "Selected track: $TRACK"
        echo "RELEASE_TRACK=$TRACK" >> $GITHUB_ENV

    - name: Validate AAB File
      shell: bash
      run: |
        echo "📁 Validating AAB file"
        echo "Track: $RELEASE_TRACK"
        echo "Package Name: ${{ inputs.package-name }}"
        echo "AAB Path: ${{ inputs.aab-path }}"

        AAB_FILE="${{ inputs.aab-path }}"
        if [ ! -f "$AAB_FILE" ]; then
          echo "❌ Error: AAB file not found at $AAB_FILE"
          exit 1
        fi
        
        # Get file size for logging
        FILE_SIZE=$(ls -lh "$AAB_FILE" | awk '{print $5}')
        echo "✅ AAB file found! Size: $FILE_SIZE"

    - name: Upload to Google Play Console
      uses: r0adkll/upload-google-play@v1
      with:
        serviceAccountJsonPlainText: ${{ inputs.service-account-json }}
        packageName: ${{ inputs.package-name }}
        releaseFiles: ${{ inputs.aab-path }}
        track: ${{ env.RELEASE_TRACK }}
        status: completed

    - name: Upload Summary
      shell: bash
      run: |
        echo "🚀 Upload completed successfully!"
        echo "Package: ${{ inputs.package-name }}"
        echo "Track: $RELEASE_TRACK"
        echo "AAB: ${{ inputs.aab-path }}"
